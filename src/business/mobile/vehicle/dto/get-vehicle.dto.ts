import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';

export class GetAllVehiclesForMobileDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ example: 'Sedan' })
  vehicleType: string;

  @AutoMap()
  @ApiProperty()
  model: string;

  @AutoMap()
  @ApiProperty()
  currentOdometer: number;
}
