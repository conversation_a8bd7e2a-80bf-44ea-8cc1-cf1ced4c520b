import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';

import { GetAllVehiclesForMobileDto } from './dto/get-vehicle.dto';
import { VehiclesService } from '../../vehicle/vehicles/vehicles.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UsersRepository } from '../../user/users/infrastructure/repositories/user.repository';

@ApiTags('Mobile - Vehicles')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller({
  path: 'mobile/vehicles',
  version: '1',
})
export class MobileVehiclesController {
  constructor(
    private readonly vehiclesService: VehiclesService,
    private readonly userSRepository: UsersRepository,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all Vehicles ( no pagination)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllVehiclesForMobileDto })
  async getAllVehiclesMinimal(@Request() request: any) {
    try {
      const driverId = request.user.sub;
      const driver = await this.userSRepository.findById(driverId);

      if (!driver?.tenantId) {
        throw new Error('No tenant specified in request context');
      }

      console.log(request.user.sub);
      const data = await this.vehiclesService.getAllVehiclesMinimal(
        driver.tenantId,
      );
      return { data };
    } catch (error) {
      throw error;
    }
  }
}
